name: railops
description: "railops is a comprehensive train travel app offering real-time
  tracking, secure access, and user-friendly features for railway staff,
  contractors, and passengers."

publish_to: "none"
version: 1.30.3+52

environment:
  sdk: ">=3.4.4 <4.0.0"
dependencies:
  android_intent_plus: ^5.3.0
  chewie: ^1.10.0
  cloud_firestore: ^5.6.9
  collection: ^1.18.0
  connectivity_plus: ^6.1.0
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  data_table_2: ^2.5.15
  device_info_plus: ^11.1.1
  drag_and_drop_lists: ^0.4.1
  dropdown_search: ^5.0.3
  file_picker: ^8.1.2
  firebase_auth: ^5.6.0
  firebase_messaging: ^15.2.7
  flutter_custom_selector: ^0.0.3
  flutter_local_notifications: ^17.2.2
  flutter_map: ^7.0.2
  flutter_map_marker_popup: ^7.0.0
  geolocator: ^13.0.1
  google_sign_in: ^6.2.1
  http_parser: ^4.0.2
  image: ^4.1.3
  image_picker: ^1.1.2
  in_app_update: ^4.2.3
  intl: ^0.19.0
  js: ^0.6.7
  latlong2: ^0.9.1
  local_auth: ^2.3.0
  mime: ^1.0.0
  multi_dropdown: ^3.0.1
  multi_select_flutter: ^4.1.3
  multiselect_formfield: 0.1.7
  open_filex: ^4.7.0
  package_info_plus: ^8.1.1
  path: ^1.8.0
  path_provider: ^2.0.0
  permission_handler: ^11.3.1
  pinput: ^5.0.0
  provider: ^6.1.2
  reorderables: ^0.6.0
  shared_preferences: ^2.3.1
  syncfusion_flutter_datagrid: ^26.2.10
  table_calendar: ^3.1.2
  toggle_switch: ^2.0.0
  universal_html: ^2.2.4
  url_launcher: ^6.3.0
  uuid: ^4.5.0
  video_player: ^2.9.5
  workmanager: ^0.5.2
  firebase_core: ^3.15.1

dependency_overrides:
  workmanager:
    git:
      url: https://github.com/aldisa546/flutter_workmanager.git
      ref: upgrade/flutter-3.29
  dart_jsonwebtoken: ^2.12.2

dev_dependencies:
  flutter_launcher_icons: "^0.13.1"
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  csv: ^6.0.0
  excel: ^2.0.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"

# TEMPORARY: Disable automatic localization generation due to Flutter SDK issue
flutter:
  # generate: true  # Disabled temporarily - using manual AppLocalizations class
  assets:
    - assets/images/Verified.gif
    - assets/images/google.png
    - assets/images/verified.png
    - assets/images/train_icons/
    - assets/images/user_inside_train.png
    - assets/images/monthly_report_without_phone.png
    - assets/images/monthly_report_with_mobile.png
    - assets/images/attendance_report.png
    - assets/images/train_report.png
    - assets/images/trip_report.png
    - assets/images/logo.png
    - assets/images/attendance/1.png
    - assets/images/attendance/2.png
    - assets/images/attendance/3.png
    - assets/images/attendance/4.png
    - assets/images/attendance/5.png
    - assets/images/attendance/6.png
    - assets/images/round_trip/1.png
    - assets/images/round_trip/2.png
    - assets/images/round_trip/3.png
    - assets/images/round_trip/4.png
    - assets/images/rail_sathi_qr.png
    - assets/images/icons8-railway-car-48.png
  uses-material-design: true
