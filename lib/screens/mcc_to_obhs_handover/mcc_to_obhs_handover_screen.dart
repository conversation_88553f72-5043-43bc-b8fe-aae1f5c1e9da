import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart';
import 'package:railops/screens/mcc_to_obhs_handover/widget/CoachButton.dart';
import 'package:railops/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart';
import 'package:railops/screens/mcc_to_obhs_handover/widget/CoachHandoverStatus.dart';
import 'package:railops/services/mcc_to_obhs_handover_services/mcc_to_obhs_handover_services.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/widgets/index.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// NOTE: Localization for this file is partially blocked due to Flutter SDK compatibility issues.
// The flutter gen-l10n command is currently failing with "Null check operator used on a null value" error.
// Most strings are already localized, but 3 remaining hardcoded strings need ARB entries:
// - "No coaches found." -> text_no_coaches_found
// - "Please select a train and date." -> text_please_select_train_and_date
// - "Please select a train and date first" -> error_please_select_train_date_first
// TODO: Complete localization implementation once Flutter gen-l10n is working properly.

class MCCToOBHSHandoverScreen extends StatefulWidget {
  const MCCToOBHSHandoverScreen({super.key});

  @override
  _MCCToOBHSHandoverScreenState createState() =>
      _MCCToOBHSHandoverScreenState();
}

class _MCCToOBHSHandoverScreenState extends State<MCCToOBHSHandoverScreen> {
  String? train;
  String? date;
  bool showLoader = false;
  List<String> coaches = [];
  Map<String, List<int>> selectedHandoverItems = {};
  Map<int, String> handoverOptions = {};
  bool isInitialLoad = true;
  List<dynamic> allHandoverItems = [];

  @override
  void initState() {
    super.initState();
    _loadAllHandoverItems();
  }

  Future<void> _loadAllHandoverItems() async {
    setState(() {
      showLoader = true;
    });

    try {
      List<dynamic> response = await TripReportServices.getAllIssues();
      setState(() {
        allHandoverItems = response;
        handoverOptions = {for (var item in response) item["id"]: item["name"]};
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(AppLocalizations.of(context)
                  .error_failed_to_load_handover_items(e.toString()))),
        );
      }
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  void onSubmit(String trainNumber, String selectedDate) async {
    setState(() {
      train = trainNumber;
      date = selectedDate;
      showLoader = true;
    });

    try {
      // final response = await MCCToOBHSHandoverServices.getHandoverReport(trainNumber, selectedDate);
      const reportFor = 'mcc_to_obhs';
      final response = await TripReportServices.getTripReport(
          trainNumber, selectedDate, "mcc_to_obhs");
      if (response.containsKey('data') &&
          response['data'].containsKey('coach_issues')) {
        setState(() {
          coaches = List<String>.from(response['data']['coach_issues'].keys);

          if (isInitialLoad) {
            selectedHandoverItems = {};
            response['data']['coach_issues'].forEach((coach, items) {
              selectedHandoverItems[coach] =
                  List<int>.from((items as List).map((e) => e['id'] as int));
            });
            isInitialLoad = false;
          } else {
            for (var coach in coaches) {
              if (!selectedHandoverItems.containsKey(coach)) {
                selectedHandoverItems[coach] = List<int>.from(
                    (response['data']['coach_issues'][coach] as List)
                        .map((e) => e['id'] as int));
              }
            }
          }
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(AppLocalizations.of(context)
                    .error_invalid_response_format)),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text("Failed to load handover report: ${e.toString()}")),
        );
      }
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  Future<void> _reloadPage() async {
    if (train != null && date != null) {
      setState(() {
        showLoader = true;
      });

      try {
        onSubmit(train!, date!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(AppLocalizations.of(context)
                    .msg_data_refreshed_successfully)),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(AppLocalizations.of(context)
                    .error_refresh_failed(e.toString()))),
          );
        }
      } finally {
        setState(() {
          showLoader = false;
        });
      }
    }
  }

  IconData getIconForHandoverItem(int itemId) {
    switch (itemId) {
      case 1:
        return Icons.checklist;
      case 2:
        return Icons.assignment;
      case 3:
        return Icons.verified_user;
      case 4:
        return Icons.security;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildCoachGrid() {
    if (coaches.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "No coaches found.",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "Please select a train and date.",
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2.0,
      ),
      itemCount: coaches.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        String coach = coaches[index];
        List<int> itemIds = selectedHandoverItems[coach] ?? [];

        return CoachButton(
          coach: coach,
          issues: itemIds,
          onTap: () => _handleHandoverUpload(coach),
          problemOptions: handoverOptions,
          isLast: index == coaches.length - 1,
        );
      },
    );
  }

  void _handleHandoverUpload(String coach) async {
    if (train == null || date == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a train and date first")),
      );
      return;
    }

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachHandoverImageUploadPage(
          trainNumber: train!,
          journeyDate: date!,
          coach: coach,
          handoverItems: handoverOptions.entries
              .where((entry) =>
                  selectedHandoverItems[coach]?.contains(entry.key) ?? false)
              .map((entry) =>
                  {'id': entry.key.toString(), 'itemName': entry.value})
              .toList(),
        ),
      ),
    );

    isInitialLoad = false;

    if (train != null && date != null) {
      onSubmit(train!, date!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title: AppLocalizations.of(context)
              .screen_title_mcc_to_obhs_handover_report),
      drawer: const CustomDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Text(AppLocalizations.of(context).text_coach_handover_report,
                style: const TextStyle(fontSize: 18)),
          ),
          Padding(
            padding: const EdgeInsets.all(5),
            child: AssignEhkCaFilters(
              onSubmit: (trainNumber, selectedDate) {
                if (trainNumber != null && selectedDate != null) {
                  setState(() {
                    isInitialLoad = true;
                  });
                  onSubmit(trainNumber, selectedDate);
                }
              },
            ),
          ),
          Expanded(
            child: showLoader
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: _reloadPage,
                    child: _buildCoachGrid(),
                  ),
          ),
        ],
      ),
    );
  }
}
