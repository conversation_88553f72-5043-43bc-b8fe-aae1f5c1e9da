// TEMPORARY WORKAROUND FOR FLUTTER GEN-L10N ISSUE
// This file provides a minimal AppLocalizations implementation
// to prevent build failures while preserving i18n infrastructure.
//
// ⚠️ IMPORTANT: This is a temporary solution!
// When Flutter SDK issue is resolved:
// 1. Delete this file
// 2. Run `flutter gen-l10n`
// 3. Restore full localization functionality

import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // CRITICAL STRINGS - Most commonly used in your app
  // Based on analysis of your converted files

  // Navigation & Menu
  String get text_home => 'Home';
  String get text_menu => 'Menu';
  String get text_train_tracker => 'Train Tracker';
  String get text_assign_ca => 'Assign CA';
  String get text_customer_care => 'Customer Care';

  // Common Actions
  String get btn_submit => 'Submit';
  String get btn_cancel => 'Cancel';
  String get btn_save => 'Save';
  String get btn_delete => 'Delete';
  String get btn_edit => 'Edit';
  String get btn_refresh => 'Refresh';
  String get btn_close => 'Close';

  // Form Labels
  String get form_train_number => 'Train Number';
  String get form_date => 'Date';
  String get form_coach => 'Coach';
  String get form_station => 'Station';

  // Error Messages
  String get error_network => 'Network error occurred';
  String get error_loading => 'Failed to load data';
  String get error_invalid_input => 'Invalid input';

  // Screen Titles (from your converted files)
  String get screen_title_mcc_to_obhs_handover_report =>
      'MCC to OBHS Handover Report';
  String get text_coach_handover_report => 'Coach Handover Report';

  // Dynamic error messages (from mcc_to_obhs_handover_screen.dart)
  String error_failed_to_load_handover_items(String error) =>
      'Failed to load handover items: $error';

  // Loading states
  String get text_loading => 'Loading...';
  String get text_please_wait => 'Please wait...';

  // Common status messages
  String get text_success => 'Success';
  String get text_error => 'Error';
  String get text_warning => 'Warning';
  String get text_info => 'Information';

  // CRITICAL MISSING STRINGS - Add as needed based on build errors

  // Add User Screen
  String get text_add_new_user => 'Add New User';
  String get text_phone_and_secondary_phone_must_be_different =>
      'Phone and secondary phone must be different';
  String get text_validation_error => 'Validation Error';
  String get text_submitting_data_please_wait =>
      'Submitting data, please wait...';
  String get text_please_complete_all_required_fields =>
      'Please complete all required fields';
  String get text_form_incomplete => 'Form Incomplete';
  String get text_first_name => 'First Name';
  String get text_enter_first_name => 'Enter first name';
  String get text_please_enter_first_name => 'Please enter first name';
  String get text_middle_name_optional => 'Middle Name (Optional)';
  String get text_enter_middle_name => 'Enter middle name';
  String get text_last_name => 'Last Name';
  String get text_enter_last_name => 'Enter last name';
  String get text_please_enter_last_name => 'Please enter last name';
  String get text_i_dont_have => "I don't have";
  String get text_phone_number => 'Phone Number';
  String get text_secondary_phone_number_optional =>
      'Secondary Phone Number (Optional)';
  String get text_whatsapp_number_same_as_phone =>
      'WhatsApp number same as phone';
  String get text_use_same_number_for_whatsapp =>
      'Use same number for WhatsApp';
  String get text_whatsapp_number => 'WhatsApp Number';
  String get text_enter_10_digit_whatsapp_number =>
      'Enter 10-digit WhatsApp number';
  String get text_please_enter_whatsapp_number =>
      'Please enter WhatsApp number';
  String get text_whatsapp_number_must_be_10_digits =>
      'WhatsApp number must be 10 digits';
  String get text_please_enter_only_numbers => 'Please enter only numbers';
  String get text_submit => 'Submit';
  String get text_request_for_add_user => 'Request for Add User';
  String get text_information_dialog_title => 'Information';
  String get text_please_complete_fields_in_order =>
      'Please complete fields in order';
  String get text_ok => 'OK';
  String get text_personal_information => 'Personal Information';
  String get text_contact_details => 'Contact Details';
  String get text_account_settings => 'Account Settings';

  // Assign EHK CA Screen
  String get text_loading_train_data => 'Loading train data...';
  String get text_loading_users => 'Loading users...';
  String get text_loading_stations => 'Loading stations...';
  String get appbar_assign_multiple_users => 'Assign Multiple Users';
  String get btn_update_both_trains => 'Update Both Trains';
  String get btn_ok => 'OK';

  // Error methods
  String error_loading_data(String error) => 'Error loading data: $error';
  String error_loading_users(String error) => 'Error loading users: $error';
  String error_loading_stations(String error) =>
      'Error loading stations: $error';
  String error_occurred(String error) => 'Error occurred: $error';

  // Feedback Screen Messages
  String get msg_failed_fetch_train_name => 'Failed to fetch train name';
  String get msg_invalid_pnr => 'Invalid PNR number';
  String get msg_pnr_not_found => 'PNR not found';
  String get msg_feedback_submitted => 'Feedback submitted successfully';
  String get msg_feedback_failed => 'Failed to submit feedback';
  String get msg_please_enter_feedback => 'Please enter your feedback';
  String get msg_please_select_rating => 'Please select a rating';
  String get msg_email_verification_sent => 'Email verification sent';
  String get msg_email_verified => 'Email verified successfully';
  String get msg_email_verification_failed => 'Email verification failed';

  // Form Labels - Additional
  String get form_pnr_number => 'PNR Number';
  String get form_feedback => 'Feedback';
  String get form_rating => 'Rating';
  String get form_email => 'Email';
  String get form_mobile => 'Mobile Number';

  // Button Labels - Additional
  String get btn_verify => 'Verify';
  String get btn_send_otp => 'Send OTP';
  String get btn_resend_otp => 'Resend OTP';
  String get btn_verify_email => 'Verify Email';
  String get btn_submit_feedback => 'Submit Feedback';

  // Status Messages
  String get status_verifying => 'Verifying...';
  String get status_sending => 'Sending...';
  String get status_processing => 'Processing...';
  String get status_completed => 'Completed';
  String get status_failed => 'Failed';

  // Placeholder for any missing strings - fallback method
  String _fallback(String key) => key.replaceAll('_', ' ').toUpperCase();
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool isSupported(Locale locale) {
    return ['en', 'hi', 'bn', 'as', 'pa', 'mr', 'kn', 'ta', 'te', 'ml']
        .contains(locale.languageCode);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
